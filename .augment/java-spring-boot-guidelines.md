---
type: "always_apply"
description: "Core guidelines for Java and Spring Boot microservices development, covering project structure, configuration, and best practices"
---

# Java + Spring Boot Guidelines

> **Cross-References**: This rule builds upon `general-coding-guidelines.md`. For post-generation validation, see `post-generation.md`. For OpenAPI specifications, see `openapi-spec-generation.md`.

## Technology Stack
- Use Java 21 with Maven-based Spring Boot application
- Use Flyway for database schema versioning
- Write all boilerplate code explicitly (no Lombok)
- Use only Spring Boot testing utilities and JUnit 5 (no Mockito)

## Health Check Implementation
- Create a dedicated `HealthController` class separate from business controllers
- Implement health endpoints: `/health`, `/health/ready`, `/health/live`
- Follow health endpoint structure: `/v1/{capability-name}/{mode}/health`
  - Use lowercase kebab-case for `capability-name`
  - Use `original` or `mock` for `mode`

## Project Structure
- Organize code in two main directories: `/original-code/` and `/mock-code/`
- Each directory contains:
  - `src/main/java/` for source files
  - `src/main/resources/` for configuration files
  - `src/test/` for test files
  - `pom.xml` for Maven configuration
  - `swagger/{capability-name}-[original|mock]-openapi.yaml` for API specs

## Package Naming Conventions
- Use reverse domain notation: `com.company.capability.layer`
- Layer-based package structure:
  - `controller` - REST controllers and web layer
  - `service` - Business logic and service layer
  - `repository` - Data access layer
  - `entity` - JPA entities and database models
  - `dto` - Data Transfer Objects for API contracts
  - `config` - Configuration classes
  - `exception` - Custom exceptions and error handling
  - `util` - Utility classes and helpers

## Configuration Class Structure
- Create separate configuration classes for different concerns:
  - `DatabaseConfig.java` - Database and JPA configuration
  - `OpenApiConfig.java` - Swagger/OpenAPI configuration
  - `SecurityConfig.java` - Security and CORS configuration
  - `ApplicationConfig.java` - General application configuration
- Use `@ConfigurationProperties` for complex configuration binding
- Validate configuration properties with `@Validated` and constraints
- Create configuration property classes with proper documentation

## Database Entity Guidelines
- Use JPA annotations for entity mapping
- Implement proper entity relationships:
  - `@OneToMany` with `mappedBy` for bidirectional relationships
  - `@ManyToOne` with `@JoinColumn` for foreign keys
  - Use `FetchType.LAZY` for performance optimization
- Include audit fields: `createdAt`, `updatedAt`, `createdBy`, `updatedBy`
- Use `@Version` for optimistic locking
- Implement `equals()` and `hashCode()` based on business keys
- Use database-generated IDs with `@GeneratedValue`

## Dependency Injection Patterns
- Use constructor injection for required dependencies
- Use setter injection only for optional dependencies
- Avoid field injection (`@Autowired` on fields)
- Create configuration classes with `@Configuration` annotation
- Use `@Value` for simple property injection
- Implement proper bean scoping (`@Singleton`, `@Prototype`, etc.)
- Create factory beans for complex object creation

## Architecture and Code Organization
- Follow layered architecture: Controllers → Services → Repositories
- Create entity classes with explicit getters and setters
- Include DTOs and Enums as needed
- Implement comprehensive test coverage:
  - Unit tests for services and utilities
  - Integration tests with H2 or Testcontainers
  - Regression tests for key business flows
