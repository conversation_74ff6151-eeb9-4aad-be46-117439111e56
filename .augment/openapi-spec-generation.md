---
type: "agent_requested"
description: "OpenAPI Specification Generation Guidelines: OpenAPI 3.0+ specification generation guidelines for main and mock applications"
---

# OpenAPI Specification Generation Guidelines

> **Cross-References**: Documents APIs built following `general-coding-guidelines.md` and `java-spring-boot-guidelines.md`. Requires services validated through `post-generation.md` and `testing-and-deployment-validation.md`.

## Objective
- Generate complete and valid OpenAPI 3.0+ specification files for each application

## File Structure Requirements
- **Main application**: `original-code/swagger/{capabilityname-original-openapi.yaml}`
- **Mock application**: `mock-code/swagger/{capabilityname-mock-openapi.yaml}`

## Purpose
- Enable accurate API documentation based on OpenAPI specifications
- Help backend teams validate API behavior
- Allow simulation of complete workflows without requiring access to real services

## Format Requirements
- Use OpenAPI 3.0 or later
- Output as standalone, human-readable YAML file (preferred over JSON)
- Do not use Swagger annotations or libraries in code
- Author specification as external file separate from code

## Content Requirements

### Info Block
- Include title, version, brief description, and contact info (if available)

### Paths Section
- Include all REST API endpoints from implemented controllers
- For each endpoint and HTTP method define:
  - Operation summary and description
  - Parameters (path, query, header)
  - Request body schema
  - Response codes (2xx, 4xx, 5xx)
  - Response schema
  - Fallback and error responses
  - Example payloads (both success and error cases)

### Components and Schemas
- Define reusable request and response schemas under `components.schemas`
- Indicate required fields, field types, formats, enums, and constraints
- Include example values for all schemas
- Include error model definitions

### Validation Rules
- Reflect required field rules, constraints, and field mismatch behaviors
- Mention fallback behavior in descriptions (retry rules, error messages)

### Security Schemes
- Define auth mechanisms under `components.securitySchemes` (Basic Auth, API Key)
- Reference in security block at global or operation level
- Skip if not applicable

## Required Endpoints
- All endpoints implemented in Controller classes
- All health check endpoints (`/health`, `/health/live`, `/health/ready`)
- All API endpoints following pattern `/v1/{capability-name}/{mode}/{operation}`

## Required Schemas
- All request and response DTOs used in controllers
- All error response models
- Health response models

## Validation Requirements
- Must be suitable for use in Swagger UI, Redoc, or importing into Postman
- Use clear formatting with consistent indentation and spacing
- Ensure all paths and schemas are fully defined with appropriate examples
- Verify that all implemented endpoints are documented

## End-to-End Integration Requirements
- Make changes only to `pom.xml`, OpenAPI YAML files, `application.yaml`, and `OpenApiConfig.java`
- Do not make other code changes
- Ensure complete end-to-end flow works correctly:
  - Server URL and port must align correctly
  - `/swagger-ui.html` page must load properly
  - All documented endpoints appear and are accessible via Swagger UI
  - API requests through Swagger UI return correct response payloads per specification
  - Handle CORS settings properly within `OpenApiConfig.java` file only
  - Ensure no CORS failures in Swagger UI
