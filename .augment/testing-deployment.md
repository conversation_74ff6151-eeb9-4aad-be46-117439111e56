---
type: "agent_requested"
description: "Testing and Deployment Guidelines: Manual testing and deployment validation guidelines for mock and main services"
---
# Testing and Deployment Guidelines

> **Cross-References**: Validates implementations from `general-coding-guidelines.md` and `java-spring-boot-guidelines.md`. Builds upon `post-generation.md` validation. Prepares services for `openapi-spec-generation.md`.

## Testing Requirements
- Never generate test files automatically
- Test both mock and main services manually
- Find and debug every issue according to provided prompts

## Test Execution Commands
- Test mock service: `cd /path/to/<mock-service>` then `mvn clean test`
- Test main service: `cd /path/to/<main-service>` then `mvn clean test`

## Test Failure Debugging Steps
- Check test data patterns defined in service specification
- Resolve dependency issues using `mvn dependency:resolve`
- Review logs and stack traces for root cause analysis
- Verify correctness of mock responses and service expectations

## Service Deployment Order
1. **Database Setup**
   - Verify PostgreSQL is running: `psql -U postgres -c '\l'`
   - Start if needed: `sudo service postgresql start`
   - Create missing database: `psql -U postgres -c 'CREATE DATABASE <db-name>;'`

2. **Mock Service Startup**
   - Navigate to mock service directory: `cd /path/to/<mock-service>`
   - Start service: `mvn spring-boot:run` (typically port 8083)
   - Check for port conflicts: `lsof -i :8083`

3. **Main Service Startup**
   - Navigate to main service directory: `cd /path/to/<main-service>`
   - Start service: `mvn spring-boot:run` (typically port 8080)
   - Check for port conflicts: `lsof -i :8080`

## Service Startup Debugging
- Check port conflicts and review error logs
- Verify `application.yml` configurations are correct
- Ensure required services (database) are reachable



## Service Validation Steps

### Health Check Testing
- Test main service health: `curl http://localhost:8080/v1/<capability-name>/original/health`
- Test mock service health: `curl http://localhost:8083/v1/<capability-name>/mock/health`

### API Endpoint Testing
- Test sample request (modify based on service specification):
  ```bash
  curl -X POST http://localhost:8080/v1/<capability-name>/check \
    -H "Content-Type: application/json" \
    -d '{"sampleKey":"sampleValue"}'
  ```
- Verify expected response structure, status code, and trace headers

## Common Test Failures and Solutions

### Port Conflict Issues
- **Symptom**: "Address already in use" error
- **Fix**: Use `lsof -i :<port>` then `kill -9 <PID>`

### Service Connectivity Problems
- **Symptom**: Main service can’t connect to mock service
- **Fix**: Check mock base URL configuration in `application.yml`

### Database Connection Issues
- **Symptom**: Database errors during service startup
- **Fix**: Ensure database is running and exists

### Dependency Resolution Problems
- **Symptom**: ClassNotFound or build errors
- **Fix**: Run `mvn dependency:resolve` or `mvn dependency:tree`

### Test Data Format Issues
- **Symptom**: Tests fail in CI/CD but pass locally
- **Fix**: Ensure test data matches pattern defined in business logic

## Automated Error Resolution Commands

### Kill Conflicting Processes
- Check port usage: `lsof -i :8080` and `lsof -i :8083`
- Kill processes: `kill -9 <PID>`

### Database Setup
- Start database: `sudo service postgresql start`
- Create database: `psql -U postgres -c 'CREATE DATABASE <db-name>;'`

### Dependency Resolution
- Resolve dependencies: `mvn dependency:resolve`

## Final Verification Checklist
- All services start without error
- Health endpoints return 200 OK status
- All tests pass locally
- Manual API tests return expected results
- Logs show masked PII (if applicable)
- Retry logic functions as expected (if implemented)
- X-Trace-Id header is present in responses
- Admin/monitoring endpoints function as expected