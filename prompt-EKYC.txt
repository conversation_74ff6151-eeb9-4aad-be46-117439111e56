Business Flow

Phase 1: eKYC Request Initiation

    Trigger:
        - The process begins when a user or system requests an eKYC verification.

    Request Payload:
        - Aadhaar or VID (12-digit)
        - Flag indicating the ID type ("AADHAAR" or "VID")
        - Mandatory identity verification consent
        - Optional consent for accessing resident's mobile/email
        - Session ID (for tracing interaction)
        - Optional parent process ID

    Validations:
        - The ID must be exactly 12 numeric digits.
        - ID type must be explicitly defined and supported.
        - Identity verification consent is mandatory.
        - Mobile/email consent must be either "YES" or "NO".
        - All fields are validated at input, and immutability of key values (ID, consent, session ID) is enforced after creation.

    API Response Processing / System:
        - A new eKYC request record is created and persisted.
        - The system calls the external Aadhaar API to initiate eKYC.
        - If the response is successful:
            - Status is updated to "IN_PROGRESS".
            - A reference number is recorded.
            - OTP is forwarded to the user based on consent preferences.
        - If an error occurs:
            - Status is marked as "FAILED".
            - Error details are logged for audit and debugging.

Phase 2: OTP Verification
    Trigger:
        - Client submits an OTP verification request containing:
            - OTP code
            - eKYC reference number

    Validations:
        - OTP must be a 6-digit numeric value.
        - eKYC request must exist and be valid for OTP verification.

    API Response Processing / System:
        - OTP details are persisted in a separate verification record.
        - OTP is forwarded to UIDAI for validation.

    System Behavior Based on API Response:
        - On success:
            - Verification status is set to "VERIFIED".
            - No PII is stored; only hashed response is saved for auditing.
        - On failure:
            - Status is set to "FAILED".
            - Reason is logged (e.g., INVALID_OTP, EXPIRED_OTP).
            - UIDAI response is hashed and stored securely.

Phase 3: Identity Verification Response

    System Behavior Based on API Response:
        - Verification status values: VERIFIED, FAILED, IN_PROGRESS
        - Timestamps and session traceability maintained
        - Personally identifiable details processed only if explicitly consented to
        - Outgoing responses are structured, sanitized, and logged securely.

Configuration-Driven Architecture:
    - Endpoint URLs for external API calls are configurable externally.
    - Switching between real and mock APIs is done via configuration without code changes.

